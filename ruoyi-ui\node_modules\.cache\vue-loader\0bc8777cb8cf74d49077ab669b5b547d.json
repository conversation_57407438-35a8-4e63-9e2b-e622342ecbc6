{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=style&index=0&id=299e884e&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1756087391951}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgIkAvYXNzZXRzL3N0eWxlcy9jdXN0b21Gb3JtIjsKCi5zZXJ2ZXItZGlzcGxheSB7CiAgbGluZS1oZWlnaHQ6IDEuODsKICBkaXNwbGF5OiBmbGV4Owp9Cgouc2VydmVyLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIHBhZGRpbmc6IDAgNXB4Owp9Cg=="}, {"version": 3, "sources": ["OperationSystemDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAotBA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "OperationSystemDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in assetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {flattenTreeData, flattenTreeToArray, getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus', 'coverArea'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      if(field.fieldKey === 'uodTime'){\n        if(!this.form[field.fieldKey]){\n          return ''\n        }else {\n          return dayjs(this.form[field.fieldKey]).format('YYYY-MM-DD HH:mm:ss')\n        }\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down',\n        coverArea: 'cover_area'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"]}]}