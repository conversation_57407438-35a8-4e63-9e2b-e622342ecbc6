use aqsoc;
-- 1.通知公告新增字段
ALTER TABLE sys_notice ADD COLUMN is_top CHAR(1) DEFAULT '0' COMMENT '是否置顶(0否 1是)';
ALTER TABLE sys_notice ADD COLUMN is_popup CHAR(1) DEFAULT '0' COMMENT '是否弹窗(0否 1是)';
ALTER TABLE sys_notice ADD COLUMN deadline DATETIME DEFAULT NULL COMMENT '截止时间';
ALTER TABLE sys_notice ADD COLUMN popup_content TEXT DEFAULT NULL COMMENT '弹窗文案';
ALTER TABLE sys_notice ADD COLUMN accept_type VARCHAR(20) DEFAULT 'all' COMMENT '接收对象类型(all/role/dept/user)';
ALTER TABLE sys_notice ADD COLUMN accept_ids TEXT DEFAULT NULL COMMENT '接收对象ID列表(JSON格式)';
-- 将accept_id迁移到accept_ids，并设置accept_type为user
UPDATE sys_notice
SET accept_ids = CONCAT('[', accept_id, ']'),
    accept_type = 'user'
WHERE accept_id IS NOT NULL AND accept_id != -1;

-- 将accept_id为-1的记录设置为全部用户
UPDATE sys_notice
SET accept_type = 'all'
WHERE accept_id = -1;

-- 删除accept_id字段
ALTER TABLE sys_notice DROP COLUMN accept_id;

-- 新增用户ID和部门ID字段，用于通知公告的权限判断
ALTER TABLE sys_notice ADD COLUMN user_id BIGINT COMMENT '创建人ID';
ALTER TABLE sys_notice ADD COLUMN dept_id BIGINT COMMENT '创建人部门ID';

ALTER TABLE sys_notice MODIFY COLUMN notice_type char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '公告类型（1通知公告 2通报公告）';


-- 2. 新建sys_notice_entrance表（相关入口）
CREATE TABLE sys_notice_entrance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    notice_id BIGINT NOT NULL COMMENT '通知公告ID',
    entrance_type VARCHAR(20) NOT NULL COMMENT '入口类型：link/shortcut/affair',
    entrance_id VARCHAR(50) COMMENT '入口ID（快捷入口或事务ID）',
    entrance_name VARCHAR(100) DEFAULT NULL COMMENT '显示名称',
    entrance_url VARCHAR(500) DEFAULT NULL COMMENT '链接地址',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    INDEX idx_notice_id (notice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告相关入口关联表';


-- 3. 新建sys_notice_read_status表（阅读状态）
CREATE TABLE sys_notice_read_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    notice_id BIGINT NOT NULL COMMENT '通知公告ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    read_status CHAR(1) DEFAULT '0' COMMENT '阅读状态(0未读 1已读)',
    read_time DATETIME DEFAULT NULL COMMENT '阅读时间',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    UNIQUE KEY uk_notice_user (notice_id, user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告阅读状态关联表';

CREATE TABLE `tbl_attack_alarm` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                    `attack_ip` varchar(50) DEFAULT NULL COMMENT '攻击者IP',
                                    `risk_level` int DEFAULT NULL COMMENT '风险等级',
                                    `location` varchar(10) DEFAULT NULL COMMENT '地理位置',
                                    `victim_ip_nums` bigint DEFAULT NULL COMMENT '攻击目标IP数',
                                    `attack_type_nums` int DEFAULT NULL COMMENT '命中规则数',
                                    `attack_nums` bigint DEFAULT NULL COMMENT '告警数量',
                                    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uni_ip_start_time` (`attack_ip`,`start_time`),
                                    KEY `idx_ip` (`attack_ip`),
                                    KEY `idx_risk_level` (`risk_level`),
                                    KEY `idx_update_time` (`update_time` DESC)
) ENGINE=InnoDB COMMENT='攻击者视角告警列表';

CREATE TABLE `tbl_attack_alarm_tags` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                         `attack_id` bigint NOT NULL COMMENT '攻击者告警ID',
                                         `tag_name` varchar(20) DEFAULT NULL COMMENT '标签',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uni_attack_tag_name` (`attack_id`,`tag_name`),
                                         KEY `idx_attack_id` (`attack_id`),
                                         KEY `idx_tag` (`tag_name`)
) ENGINE=InnoDB COMMENT='攻击者视角告警标签';

create index idx_sip on ffsafe_flow_detail (`sip`);
create index idx_sip_time on ffsafe_flow_detail (`sip`,`create_time`);

CREATE TABLE tbl_threat_deduction_standard (
                                               id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                               content_name VARCHAR(255) NOT NULL COMMENT '名称',
                                               type VARCHAR(255) COMMENT '风险类型',
                                               total_score DECIMAL(10,2) UNSIGNED COMMENT '总分值',
                                               item_total DECIMAL(10,2) UNSIGNED COMMENT '单项总分',
                                               deduction_per_event DECIMAL(10,2) UNSIGNED COMMENT '单次扣分值',
                                               critical VARCHAR(255) COMMENT '严重级别扣分',
                                               high VARCHAR(255) COMMENT '高危级别扣分',
                                               medium_hazard VARCHAR(255) COMMENT '中危级别扣分',
                                               low VARCHAR(255) COMMENT '低危级别扣分',
                                               created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               modified_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) COMMENT='风险威胁扣分标准表';


CREATE TABLE tbl_deduction_detail (
                                      id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
                                      deduction_date datetime COMMENT '扣分日期',
                                      deduction_type VARCHAR(64) COMMENT '扣分类型',
                                      deduction_level VARCHAR(64) COMMENT '扣分等级',
                                      deduction_score DECIMAL(10,1) UNSIGNED COMMENT '扣分分数',
                                      user_id bigint COMMENT '用户ID',
                                      department_id bigint  COMMENT '部门ID',
                                      risk_type VARCHAR(64) COMMENT '风险类型',
                                      reference_id VARCHAR(64) COMMENT '关联ID',
                                      created_time datetime COMMENT '记录创建时间',
                                      created_by VARCHAR(64) COMMENT '记录操作人',
                                      delete_time datetime COMMENT '删除时间',
                                      is_del varchar(64) COMMENT '是否删除0=删除1=未删除',
                                      INDEX idx_date (deduction_date),
                                      INDEX idx_user (user_id, deduction_date),
                                      INDEX idx_dept (department_id, deduction_date),
                                      INDEX idx_type_level (deduction_type, deduction_level)
) COMMENT='扣分详情记录表';


-- 快捷入口表
CREATE TABLE sys_shortcut_entrance (
  entrance_id       BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '入口ID',
  entrance_category VARCHAR(100)     NOT NULL                   COMMENT '入口分类（字典entrance_category_type）',
  entrance_name     VARCHAR(100)    NOT NULL                   COMMENT '入口名称',
  menu_id           BIGINT(20)      DEFAULT NULL               COMMENT '关联菜单ID（关联sys_menu表的menu_id）',
  icon_url          VARCHAR(500)    DEFAULT ''                 COMMENT '入口图标URL地址',
  status            CHAR(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  PRIMARY KEY (entrance_id),
  KEY idx_entrance_category (entrance_category),
  KEY idx_entrance_status (status),
  KEY idx_entrance_menu (menu_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT = '快捷入口表';


-- 首页快捷入口配置表
CREATE TABLE sys_homepage_entrance (
  id                BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
  entrance_id       BIGINT(20)      NOT NULL                   COMMENT '关联快捷入口ID（关联sys_shortcut_entrance表的entrance_id）',
  sort_order        INT             DEFAULT 0                  COMMENT '首页显示排序（数值越小越靠前）',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_entrance_id (entrance_id),
  KEY idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='首页快捷入口配置表';


-- 攻击者视角新增同步状态字段
ALTER TABLE tbl_attack_alarm
    ADD COLUMN synchronization_status VARCHAR(50) COMMENT '同步状态';

-- 资产字段配置
CREATE TABLE `tbl_asset_fields` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                    `form_ref` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '表单ref',
                                    `form_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '表单名称',
                                    `asset_type` int DEFAULT NULL COMMENT '资产分类 1=业务系统 2=主机 3=终端 4=网络设备 5=安全设备 6=区域边界',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE KEY `uni_ref_type` (`form_ref`,`asset_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资产字段配置';

-- 资产字段项配置
CREATE TABLE `tbl_asset_fields_item` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                         `ref_id` bigint DEFAULT NULL COMMENT '所属form分组',
                                         `field_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段名称',
                                         `field_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '字段key',
                                         `required` bit(1) DEFAULT b'0' COMMENT '是否必填',
                                         `is_query` bit(1) DEFAULT b'0' COMMENT '是否筛选',
                                         `is_show` bit(1) DEFAULT b'1' COMMENT '是否显示',
                                         `asset_type` int DEFAULT NULL COMMENT '资产类型',
                                         `sort` int DEFAULT '0' COMMENT '排序',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE KEY `uni_ref_key` (`ref_id`,`field_key`) USING BTREE,
                                         KEY `idx_ref` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资产字段项配置';

CREATE TABLE `tbl_work_order_dept` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                       `work_order_id` bigint DEFAULT NULL COMMENT '通报ID',
                                       `dept_id` bigint DEFAULT NULL COMMENT '部门',
                                       `dept_name` varchar(50) DEFAULT NULL COMMENT '部门名称',
                                       `handle_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处置标题',
                                       `event_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理结果',
                                       `handle_situation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理情况',
                                       `other_situation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '其他情况',
                                       `feedback_file_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '反馈附件',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uni_dept` (`work_order_id`,`dept_id`),
                                       KEY `idx_order` (`work_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通报部门';

CREATE TABLE `tbl_work_order_target` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                         `work_order_id` bigint DEFAULT NULL COMMENT '通报ID',
                                         `work_order_dept_id` bigint DEFAULT NULL COMMENT '通报部门ID',
                                         `handle_dept` bigint DEFAULT NULL COMMENT '处置部门',
                                         `handle_user` bigint DEFAULT NULL COMMENT '处置人',
                                         `application_id` bigint DEFAULT NULL COMMENT '业务系统',
                                         `login_url` varchar(255) DEFAULT NULL COMMENT '登录地址',
                                         `manager` bigint DEFAULT NULL COMMENT '责任人',
                                         `phone` varchar(30) DEFAULT NULL COMMENT '联系方式',
                                         `handle_status` int DEFAULT NULL COMMENT '处置状态',
                                         `handle_time` datetime DEFAULT NULL COMMENT '处置时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_work_order` (`work_order_id`),
                                         KEY `idx_work_dept` (`work_order_dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通报对象';

CREATE TABLE `tbl_work_order_event` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                        `work_order_id` bigint DEFAULT NULL COMMENT '通报ID',
                                        `work_order_target_id` bigint DEFAULT NULL COMMENT '通报对象ID',
                                        `type` int DEFAULT NULL COMMENT '事件类型 1=IP 2=WEB 3=威胁 4=弱口令',
                                        `event_id` bigint DEFAULT NULL COMMENT '事件ID',
                                        `handle_state` int DEFAULT NULL COMMENT '事件处置状态',
                                        `check_state` int DEFAULT NULL COMMENT '事件处置核验状态',
                                        `handle_desc` varchar(500) DEFAULT NULL COMMENT '处置说明',
                                        `handle_file` text COMMENT '处置材料',
                                        `check_desc` varchar(500) DEFAULT NULL COMMENT '核验说明',
                                        `check_file` text COMMENT '核验材料',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_order` (`work_order_id`),
                                        KEY `idx_target` (`work_order_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通报事件';

ALTER TABLE tbl_work_backlog
    ADD COLUMN id bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    ADD COLUMN is_completion int DEFAULT NULL COMMENT '是否完成',
    ADD COLUMN completion_time datetime DEFAULT NULL COMMENT '完成时间',
    ADD COLUMN node_id varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属节点',
    ADD COLUMN flow_state varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程状态';

ALTER TABLE tbl_work_order MODIFY COLUMN remark2 text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '预留字段2';

